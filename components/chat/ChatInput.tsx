'use client';

import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Send, Image, X, Loader2, AlertCircle, FileImage } from 'lucide-react';
import { messageSchema, MessageFormData, validateImageFile } from '@/lib/validations';
import { useUIStore } from '@/stores/uiStore';
import { cn, isValidImageFile, formatFileSize } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (data: MessageFormData) => Promise<void>;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message...",
  maxLength = 1000
}: ChatInputProps) {
  const { showToast } = useUIStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [imageError, setImageError] = useState<string | null>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragRef = useRef<HTMLDivElement>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    clearErrors,
    formState: { errors, isValid },
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    mode: 'onChange',
    defaultValues: {
      content: '',
      image: undefined,
    },
  });

  const content = watch('content');
  const contentLength = content?.length || 0;

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [content]);

  // Focus textarea on mount
  useEffect(() => {
    if (textareaRef.current && !disabled) {
      textareaRef.current.focus();
    }
  }, [disabled]);

  // Enhanced image validation and processing
  const processImageFile = useCallback((file: File) => {
    setImageError(null);

    // Validate image file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      setImageError(validation.error || 'Invalid image file');
      showToast({
        type: 'error',
        message: validation.error || 'Invalid image file',
      });
      return false;
    }

    setSelectedImage(file);
    setValue('image', file, { shouldValidate: true });
    clearErrors('image');

    // Create preview with error handling
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      if (result) {
        setImagePreview(result);
      }
    };
    reader.onerror = () => {
      setImageError('Failed to read image file');
      showToast({
        type: 'error',
        message: 'Failed to read image file',
      });
    };
    reader.readAsDataURL(file);
    return true;
  }, [setValue, clearErrors, showToast]);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    processImageFile(file);
  };

  const handleRemoveImage = useCallback(() => {
    setSelectedImage(null);
    setImagePreview(null);
    setImageError(null);
    setValue('image', undefined, { shouldValidate: true });
    clearErrors('image');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [setValue, clearErrors]);

  // Drag and drop functionality
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || isSubmitting) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      const file = files[0];
      if (file.type.startsWith('image/')) {
        processImageFile(file);
      } else {
        showToast({
          type: 'error',
          message: 'Please drop an image file',
        });
      }
    }
  }, [disabled, isSubmitting, processImageFile, showToast]);

  // Enhanced form submission with better validation and error handling
  const onSubmit = async (data: MessageFormData) => {
    if (isSubmitting || disabled) return;

    // Comprehensive validation
    const trimmedContent = data.content?.trim() || '';
    const hasContent = trimmedContent.length > 0;
    const hasImage = data.image instanceof File;

    // Check if either content or image is provided
    if (!hasContent && !hasImage) {
      showToast({
        type: 'error',
        message: 'Please enter a message or select an image',
      });
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
      return;
    }

    // Additional content validation
    if (hasContent) {
      if (trimmedContent.length > maxLength) {
        showToast({
          type: 'error',
          message: `Message is too long. Maximum ${maxLength} characters allowed.`,
        });
        return;
      }
    }

    // Additional image validation
    if (hasImage && data.image) {
      const validation = validateImageFile(data.image);
      if (!validation.isValid) {
        showToast({
          type: 'error',
          message: validation.error || 'Invalid image file',
        });
        return;
      }
    }

    try {
      setIsSubmitting(true);

      // Prepare clean data
      const cleanData: MessageFormData = {
        content: trimmedContent,
        image: hasImage ? data.image : undefined,
      };

      await onSendMessage(cleanData);

      // Reset form and state
      reset();
      handleRemoveImage();

      // Focus textarea for next message
      if (textareaRef.current) {
        textareaRef.current.focus();
      }

      showToast({
        type: 'success',
        message: 'Message sent successfully',
        duration: 2000,
      });
    } catch (error) {
      console.error('Failed to send message:', error);
      showToast({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to send message',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Enhanced keyboard handling
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(onSubmit)();
    } else if (e.key === 'Escape' && selectedImage) {
      e.preventDefault();
      handleRemoveImage();
    }
  };

  // Enhanced send button state logic
  const canSend = useMemo(() => {
    const hasValidContent = (content?.trim() || '').length > 0 && contentLength <= maxLength;
    const hasValidImage = selectedImage && !imageError;
    return (hasValidContent || hasValidImage) && !isSubmitting && !disabled && isValid;
  }, [content, contentLength, maxLength, selectedImage, imageError, isSubmitting, disabled, isValid]);

  return (
    <div
      ref={dragRef}
      className={cn(
        "border-t border-gray-200 bg-white p-4 transition-colors",
        dragActive && "bg-blue-50 border-blue-300"
      )}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {/* Drag Overlay */}
      {dragActive && (
        <div className="absolute inset-0 bg-blue-100 bg-opacity-50 flex items-center justify-center z-10 pointer-events-none">
          <div className="bg-white p-4 rounded-lg shadow-lg border-2 border-dashed border-blue-400">
            <FileImage className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="text-sm text-blue-700 font-medium">Drop image here</p>
          </div>
        </div>
      )}

      {/* Image Preview */}
      {imagePreview && (
        <div className="mb-3">
          <div className="relative inline-block">
            <img
              src={imagePreview}
              alt="Preview"
              className={cn(
                "max-w-32 max-h-32 rounded-lg border-2 transition-colors",
                imageError ? "border-red-300" : "border-gray-200"
              )}
            />
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              title="Remove image"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
          {selectedImage && (
            <div className="mt-2">
              <p className="text-xs text-gray-600">
                {selectedImage.name} ({formatFileSize(selectedImage.size)})
              </p>
              {imageError && (
                <p className="text-xs text-red-600 flex items-center gap-1 mt-1">
                  <AlertCircle className="w-3 h-3" />
                  {imageError}
                </p>
              )}
            </div>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-3">
        {/* Message Input Container */}
        <div className="flex items-end gap-2">
          {/* Message Input */}
          <div className="flex-1 relative">
            <textarea
              {...register('content')}
              ref={textareaRef}
              placeholder={placeholder}
              onKeyDown={handleKeyDown}
              disabled={disabled || isSubmitting}
              rows={1}
              maxLength={maxLength}
              className={cn(
                "w-full px-3 py-2 pr-20 border rounded-lg resize-none transition-all duration-200",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "placeholder:text-gray-400",
                errors.content
                  ? "border-red-300 bg-red-50 focus:ring-red-500"
                  : "border-gray-300 bg-white",
                (disabled || isSubmitting) && "bg-gray-50 cursor-not-allowed opacity-60",
                contentLength > maxLength * 0.9 && "border-yellow-300 bg-yellow-50"
              )}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />

            {/* Character Count */}
            <div className="absolute bottom-1 right-12 text-xs text-gray-400">
              <span className={cn(
                contentLength > maxLength * 0.9 && "text-yellow-600",
                contentLength > maxLength && "text-red-600"
              )}>
                {contentLength}/{maxLength}
              </span>
            </div>

            {/* Image Upload Button */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isSubmitting}
              className={cn(
                "absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded transition-colors",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                selectedImage
                  ? "text-blue-600 bg-blue-50 hover:bg-blue-100"
                  : "text-gray-400 hover:text-gray-600 hover:bg-gray-50",
                (disabled || isSubmitting) && "opacity-50 cursor-not-allowed"
              )}
              title="Attach image (or drag & drop)"
            >
              <Image className="w-4 h-4" />
            </button>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
              onChange={handleImageSelect}
              className="hidden"
            />
          </div>

          {/* Send Button */}
          <button
            type="submit"
            disabled={!canSend}
            className={cn(
              "p-2.5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",
              canSend
                ? "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md"
                : "bg-gray-300 text-gray-500 cursor-not-allowed",
              isSubmitting && "scale-95"
            )}
            title={canSend ? "Send message (Enter)" : "Enter a message to send"}
          >
            {isSubmitting ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Error Messages */}
        {(errors.content || errors.image) && (
          <div className="space-y-1">
            {errors.content && (
              <p className="text-xs text-red-600 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.content.message}
              </p>
            )}
            {errors.image && (
              <p className="text-xs text-red-600 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.image.message}
              </p>
            )}
          </div>
        )}

        {/* Keyboard Shortcuts and Tips */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <span>
              <kbd className="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">Enter</kbd> to send
            </span>
            <span>
              <kbd className="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">Shift + Enter</kbd> for new line
            </span>
            {selectedImage && (
              <span>
                <kbd className="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">Esc</kbd> to remove image
              </span>
            )}
          </div>
          {!selectedImage && (
            <span className="text-gray-400">
              Drag & drop images or click <Image className="w-3 h-3 inline" /> to attach
            </span>
          )}
        </div>
      </form>
    </div>
  );
}
