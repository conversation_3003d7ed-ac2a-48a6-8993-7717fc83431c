'use client';

import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Send, Image, X, Loader2 } from 'lucide-react';
import { messageSchema, MessageFormData } from '@/lib/validations';
import { useUIStore } from '@/stores/uiStore';
import { cn, isValidImageFile, formatFileSize } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (data: MessageFormData) => Promise<void>;
  disabled?: boolean;
}

export function ChatInput({ onSendMessage, disabled = false }: ChatInputProps) {
  const { showToast } = useUIStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    mode: 'onChange',
    defaultValues: {
      content: '',
      image: undefined,
    },
  });

  const content = watch('content');

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, [content]);

  // Focus textarea on mount
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!isValidImageFile(file)) {
      showToast({
        type: 'error',
        message: 'Please select a valid image file (JPEG, PNG, GIF, WebP) under 5MB',
      });
      return;
    }

    setSelectedImage(file);
    setValue('image', file, { shouldValidate: true });

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setValue('image', undefined, { shouldValidate: true });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const onSubmit = async (data: MessageFormData) => {
    if (isSubmitting || disabled) return;

    // Validate that either content or image is provided
    const hasContent = data.content && data.content.trim().length > 0;
    const hasImage = data.image instanceof File;

    if (!hasContent && !hasImage) {
      showToast({
        type: 'error',
        message: 'Please enter a message or select an image',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await onSendMessage(data);
      
      // Reset form
      reset();
      setSelectedImage(null);
      setImagePreview(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      // Focus textarea
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to send message',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(onSubmit)();
    }
  };

  const canSend = ((content?.trim() || '').length > 0 || selectedImage) && !isSubmitting && !disabled;

  return (
    <div className="border-t border-gray-200 bg-white p-4">
      {/* Image Preview */}
      {imagePreview && (
        <div className="mb-3 relative inline-block">
          <img
            src={imagePreview}
            alt="Preview"
            className="max-w-32 max-h-32 rounded-lg border border-gray-200"
          />
          <button
            type="button"
            onClick={handleRemoveImage}
            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
          >
            <X className="w-3 h-3" />
          </button>
          {selectedImage && (
            <p className="text-xs text-gray-500 mt-1">
              {selectedImage.name} ({formatFileSize(selectedImage.size)})
            </p>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="flex items-end gap-2">
        {/* Message Input */}
        <div className="flex-1 relative">
          <textarea
            {...register('content')}
            ref={textareaRef}
            placeholder="Type your message..."
            onKeyDown={handleKeyDown}
            disabled={disabled || isSubmitting}
            rows={1}
            className={cn(
              "w-full px-3 py-2 pr-10 border rounded-lg resize-none transition-colors",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              "placeholder:text-gray-400",
              errors.content
                ? "border-red-300 bg-red-50"
                : "border-gray-300 bg-white",
              (disabled || isSubmitting) && "bg-gray-50 cursor-not-allowed"
            )}
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />
          
          {/* Image Upload Button */}
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || isSubmitting}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Attach image"
          >
            <Image className="w-4 h-4" />
          </button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageSelect}
            className="hidden"
          />
        </div>

        {/* Send Button */}
        <button
          type="submit"
          disabled={!canSend}
          className={cn(
            "p-2 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            canSend
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          )}
          title="Send message (Enter)"
        >
          {isSubmitting ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Send className="w-5 h-5" />
          )}
        </button>
      </form>

      {/* Error Messages */}
      {errors.content && (
        <p className="mt-1 text-xs text-red-600">{errors.content.message}</p>
      )}
      {errors.image && (
        <p className="mt-1 text-xs text-red-600">{errors.image.message}</p>
      )}

      {/* Keyboard Shortcut Hint */}
      <p className="text-xs text-gray-500 mt-2">
        Press <kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs">Enter</kbd> to send, 
        <kbd className="px-1 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs ml-1">Shift + Enter</kbd> for new line
      </p>
    </div>
  );
}
